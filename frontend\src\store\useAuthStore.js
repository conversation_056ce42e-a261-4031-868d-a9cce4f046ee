import {create} from 'zustand'
import {instance} from '../utils/axios.js'

export const useAuthStore = create((set) => ({
    authUser: null,
    isCheckingAuth: true,
    isSigningUp: false,
    isLoggingIn: false,
    isUpdatingProfile: false,
    
    checkAuth : async() =>{
        try {
            const res = await instance.get("/auth/check-auth");
            set({authUser: res.data, isCheckingAuth: false});
        } catch (error) {
            set({authUser: null, isCheckingAuth: false});
        }
    },

    signup: async(data)=>{
        
    }
}))
