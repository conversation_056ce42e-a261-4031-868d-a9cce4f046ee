import React, { useEffect } from 'react'
import NavBar from './components/NavBar.jsx'
import { Routes, Route, Navigate } from 'react-router-dom'
import Home from './pages/Home.jsx'
import Login from './pages/Login.jsx'
import SignUp from './pages/SignUp.jsx'
import SettingsPage from './pages/SettingsPage.jsx'
import Profile from './pages/Profile.jsx'
import { useAuthStore } from './store/useAuthStore.js'
import { Loader } from 'lucide-react'

function App() {
  const {authUser, checkAuth, isCheckingAuth} = useAuthStore();

  useEffect(()=>{
    checkAuth();  
  },[checkAuth])
  
  if(isCheckingAuth && !authUser){
    return (
      <div className='flex items-center justify-center h-screen'>
        <Loader className="size-10 animate-spin"/>
      </div>
    );
  }
  return (
    <div>
      <NavBar/>
      <Routes>
        <Route path="/" element={authUser ? <Home/> : <Navigate to="/login"/>}/>
        <Route path="/signup" element={!authUser ? <SignUp/> : <Navigate to="/"/>}/>
        <Route path="/login" element={!authUser ? <Login/> : <Navigate to="/"/>}/>
        <Route path="/settings" element={<SettingsPage/>}/>
        <Route path="/profile" element={authUser ?<Profile/>: <Navigate to="/login"/>}/>
      </Routes>
    </div>
  )
}

export default App
