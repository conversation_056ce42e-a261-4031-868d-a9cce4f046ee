import dotenv from "dotenv";
dotenv.config();
import express from "express";
import authRoutes from "./routes/auth.routes.js";
import { connectDB } from "./db/db.js";
import cookieParser from "cookie-parser";
import messageRoutes from "./routes/message.routes.js";
import cors from "cors";
import { app,io, server } from "./utils/socket.js";



const port = process.env.PORT;

// Middleware for parsing request bodies
app.use(express.json());
app.use(express.urlencoded({extended: true}));
app.use(cookieParser());
app.use(cors({
  origin: "http://localhost:5173",
  credentials: true,
}));

app.get("/", (req, res) => {
  res.send("Hello World!");
});

app.use("/api/auth",authRoutes);
app.use("/api/message",messageRoutes);

server.listen(port, () => {
  console.log(`app listening on port ${port}`);
  connectDB();
});
