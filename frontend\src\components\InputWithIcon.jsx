import React from 'react';

function InputWithIcon({ icon: Icon, placeholder, type = "text", value, onChange, className = "" }) {
  return (
    <div className="relative">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-20">
        <Icon className="w-5 h-5 text-gray-500" />
      </div>
      <input
        type={type}
        className={`w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className}`}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
      />
    </div>
  );
}

export default InputWithIcon;
