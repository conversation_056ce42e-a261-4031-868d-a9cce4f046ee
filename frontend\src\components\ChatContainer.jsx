import React, { useEffect, useState } from 'react'
import { useChatStore } from '../store/useChatStore.js'
import { useAuthStore } from '../store/useAuthStore.js'
import ChatHeader from './ChatHeader.jsx'
import MessageInput from './MessageInput.jsx'
import MessageSkeleton from './skeletons/MessageSkeleton.jsx'

function ChatContainer() {
    const {messages, getMessages, isMessageLoading, selectedUsers} = useChatStore();
    const {authUser} = useAuthStore();

    useEffect(()=>{
        getMessages(selectedUsers._id);
    },[selectedUsers._id,getMessages])
    if(isMessageLoading){ return (
      <div className='flex-1 flex flex-col overflow-hidden'>
        <ChatHeader/>
        <MessageSkeleton/>
        <MessageInput/>
      </div>
    )}


  return (
    <div className='flex-1 flex flex-col overflow-hidden'>
      <ChatHeader/>
      <div className='flex-1 overflow-y-auto p-4 space-y-4'>
        {messages.map((message)=>{
          <div key={message._id} 
          className={`chat ${message.senderId === authUser._id ? "chat-end" : "chat-start"}`}>
            <div className='chat-image avatar'>
              <div className='size-10 rounded-full border'>
                <img src={message.senderId === authUser._id ? authUser.profilepic || "/avatar.png" : selectedUsers.profilepic || "/avatar.png"}/>

              </div>
            </div>
            <div className='chat-header mb-1'>
              <time datetime="text-xs opacity-50 ml-1">
                {message.createdAt ? formatDistanceToNow(message.createdAt) : ""}
              </time>
            </div>
          </div>
        })}
      </div>
      <MessageInput/>
    </div>
  )
}

export default ChatContainer
