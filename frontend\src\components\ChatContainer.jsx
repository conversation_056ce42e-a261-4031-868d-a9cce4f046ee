import React, { useEffect } from 'react'
import { useChatStore } from '../store/useChatStore.js'
import ChatHeader from './ChatHeader.jsx'
import MessageInput from './MessageInput.jsx'
import MessageSkeleton from './skeletons/MessageSkeleton.jsx'

function ChatContainer() {
    const {messages, getMessages, isMessageLoading, selectedUsers} = useChatStore();

    useEffect(()=>{
        getMessages(selectedUsers._id);
    },[selectedUsers._id,getMessages])
    if(isMessageLoading){ return (
      <div className='flex-1 flex flex-col overflwo-auto'>
        <ChatHeader/>
        <MessageSkeleton/>
        <MessageInput/>
      </div>
    )}


  return (
    <div>
      <div className='flex flex-1 flex-col overflow-auto'>
        <ChatHeader/>
        <p>messages... </p>
        <MessageInput/>
      </div>
    </div>
  )
}

export default ChatContainer
