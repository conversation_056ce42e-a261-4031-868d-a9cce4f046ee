import React, { useEffect } from 'react'
import { useChatStore } from '../store/useChatStore.js'
import ChatHeader from './ChatHeader.jsx'
import MessageInput from './MessageInput.jsx'
import MessageSkeleton from './skeletons/MessageSkeleton.jsx'

function ChatContainer() {
    const {messages, getMessages, isMessageLoading, selectedUsers} = useChatStore();

    useEffect(()=>{
        getMessages(selectedUsers._id);
    },[selectedUsers._id,getMessages])
    if(isMessageLoading){ return (
      <div className='flex-1 flex flex-col overflow-hidden'>
        <ChatHeader/>
        <MessageSkeleton/>
        <MessageInput/>
      </div>
    )}


  return (
    <div className='flex-1 flex flex-col overflow-hidden'>
      <ChatHeader/>
      <div className='flex-1 overflow-y-auto p-4 space-y-4'>
        {messages.map((message) => (
          <div key={message._id} className="chat">
            <div className="chat-bubble">{message.text}</div>
          </div>
        ))}
      </div>
      <MessageInput/>
    </div>
  )
}

export default ChatContainer
