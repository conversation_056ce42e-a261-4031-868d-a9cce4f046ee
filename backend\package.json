{"name": "backend", "version": "1.0.0", "description": "", "main": "src/index.js", "scripts": {"dev": "nodemon src/index.js", "seed": "node src/seeds/user.seeds.js", "seed:clear": "node src/seeds/clear-and-seed.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.2", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}